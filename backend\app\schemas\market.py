"""
海天AI纳斯达克交易系统 - 市场数据Pydantic模型
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 数据管道使用的Pydantic模型定义
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, field_validator, ConfigDict


class MarketData(BaseModel):
    """市场数据Pydantic模型"""
    
    symbol: str = Field(..., description="标的代码")
    current_price: Decimal = Field(..., description="当前价格")
    open_price: Optional[Decimal] = Field(None, description="开盘价")
    high_price: Optional[Decimal] = Field(None, description="最高价")
    low_price: Optional[Decimal] = Field(None, description="最低价")
    volume: int = Field(0, description="成交量")
    turnover: Optional[float] = Field(None, description="成交额")
    change: Optional[Decimal] = Field(None, description="涨跌额")
    change_pct: Optional[float] = Field(None, description="涨跌幅(%)")
    bid_price: Optional[Decimal] = Field(None, description="买一价")
    ask_price: Optional[Decimal] = Field(None, description="卖一价")
    bid_volume: int = Field(0, description="买一量")
    ask_volume: int = Field(0, description="卖一量")
    spread: Optional[Decimal] = Field(None, description="买卖价差")
    spread_pct: Optional[float] = Field(None, description="买卖价差百分比")
    timestamp: Optional[datetime] = Field(None, description="数据时间戳")
    
    @field_validator('current_price', 'open_price', 'high_price', 'low_price', 'bid_price', 'ask_price', 'change', 'spread')
    @classmethod
    def validate_prices(cls, v):
        """验证价格字段"""
        if v is not None and v < 0:
            raise ValueError("价格不能为负数")
        return v
    
    @field_validator('volume', 'bid_volume', 'ask_volume')
    @classmethod
    def validate_volumes(cls, v):
        """验证成交量字段"""
        if v < 0:
            raise ValueError("成交量不能为负数")
        return v
    
    model_config = ConfigDict(
        # 在Pydantic V2中，json_encoders已弃用，使用自定义序列化器
        # 这里我们依赖Pydantic V2的默认序列化行为
        # Decimal会自动转换为float，datetime会自动转换为ISO格式
    )


class TickData(BaseModel):
    """Tick数据Pydantic模型"""
    
    symbol: str = Field(..., description="标的代码")
    price: Decimal = Field(..., description="成交价格")
    volume: int = Field(..., description="成交量")
    direction: Literal["buy", "sell", "neutral"] = Field(..., description="买卖方向")
    timestamp: Optional[datetime] = Field(None, description="成交时间")
    
    @field_validator('price')
    @classmethod
    def validate_price(cls, v):
        """验证价格"""
        if v <= 0:
            raise ValueError("价格必须大于0")
        return v

    @field_validator('volume')
    @classmethod
    def validate_volume(cls, v):
        """验证成交量"""
        if v <= 0:
            raise ValueError("成交量必须大于0")
        return v
    
    model_config = ConfigDict(
        # 在Pydantic V2中，json_encoders已弃用，使用自定义序列化器
        # 这里我们依赖Pydantic V2的默认序列化行为
        # Decimal会自动转换为float，datetime会自动转换为ISO格式
    )


class TechnicalIndicator(BaseModel):
    """技术指标Pydantic模型"""
    
    symbol: str = Field(..., description="标的代码")
    indicator_name: str = Field(..., description="指标名称")
    indicator_value: Decimal = Field(..., description="指标值")
    period: int = Field(..., description="计算周期")
    timestamp: Optional[datetime] = Field(None, description="计算时间")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")
    
    @field_validator('period')
    @classmethod
    def validate_period(cls, v):
        """验证周期"""
        if v <= 0:
            raise ValueError("计算周期必须大于0")
        return v
    
    model_config = ConfigDict(
        # 在Pydantic V2中，json_encoders已弃用，使用自定义序列化器
        # 这里我们依赖Pydantic V2的默认序列化行为
        # Decimal会自动转换为float，datetime会自动转换为ISO格式
    )


class MarketDataResponse(BaseModel):
    """市场数据响应模型"""
    
    success: bool = Field(..., description="是否成功")
    data: Optional[MarketData] = Field(None, description="市场数据")
    message: Optional[str] = Field(None, description="消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class TechnicalIndicatorResponse(BaseModel):
    """技术指标响应模型"""
    
    success: bool = Field(..., description="是否成功")
    data: Optional[TechnicalIndicator] = Field(None, description="技术指标数据")
    message: Optional[str] = Field(None, description="消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class MarketDataBatch(BaseModel):
    """批量市场数据模型"""
    
    symbol: str = Field(..., description="标的代码")
    data_list: list[MarketData] = Field(..., description="市场数据列表")
    total_count: int = Field(..., description="总数量")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")


class TechnicalIndicatorBatch(BaseModel):
    """批量技术指标模型"""
    
    symbol: str = Field(..., description="标的代码")
    indicator_name: str = Field(..., description="指标名称")
    data_list: list[TechnicalIndicator] = Field(..., description="技术指标列表")
    total_count: int = Field(..., description="总数量")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")


class DataPipelineStatus(BaseModel):
    """数据管道状态模型"""
    
    is_running: bool = Field(..., description="是否运行中")
    start_time: Optional[datetime] = Field(None, description="启动时间")
    runtime_seconds: float = Field(..., description="运行时长(秒)")
    processed_count: int = Field(..., description="处理数据量")
    error_count: int = Field(..., description="错误数量")
    error_rate: float = Field(..., description="错误率")
    active_tasks: int = Field(..., description="活跃任务数")


class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    
    type: str = Field(..., description="消息类型")
    data: Dict[str, Any] = Field(..., description="消息数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="消息时间")


class DataCollectorConfig(BaseModel):
    """数据采集器配置模型"""
    
    collector_type: Literal["mock", "qmt"] = Field("mock", description="采集器类型")
    collection_interval: int = Field(1, description="采集间隔(秒)")
    symbols: list[str] = Field(default_factory=lambda: ["159509"], description="采集标的列表")
    enabled: bool = Field(True, description="是否启用")


class ProcessorConfig(BaseModel):
    """数据处理器配置模型"""
    
    enable_validation: bool = Field(True, description="是否启用数据验证")
    enable_cleaning: bool = Field(True, description="是否启用数据清洗")
    enable_anomaly_detection: bool = Field(True, description="是否启用异常检测")
    price_precision: int = Field(4, description="价格精度")


class IndicatorConfig(BaseModel):
    """技术指标配置模型"""
    
    rsi_period: int = Field(14, description="RSI周期")
    macd_fast_period: int = Field(12, description="MACD快线周期")
    macd_slow_period: int = Field(26, description="MACD慢线周期")
    macd_signal_period: int = Field(9, description="MACD信号线周期")
    ma_periods: list[int] = Field(default_factory=lambda: [5, 10, 20, 60], description="移动平均线周期")
    bollinger_period: int = Field(20, description="布林带周期")
    bollinger_std_dev: float = Field(2.0, description="布林带标准差倍数")


class StorageConfig(BaseModel):
    """存储配置模型"""
    
    enable_database_storage: bool = Field(True, description="是否启用数据库存储")
    enable_cache_storage: bool = Field(True, description="是否启用缓存存储")
    batch_size: int = Field(100, description="批处理大小")
    flush_interval: int = Field(30, description="刷新间隔(秒)")
    retention_days: int = Field(30, description="数据保留天数")


class DistributorConfig(BaseModel):
    """分发器配置模型"""
    
    enable_websocket: bool = Field(True, description="是否启用WebSocket分发")
    enable_ai_trader: bool = Field(True, description="是否启用AI交易员分发")
    max_connections: int = Field(100, description="最大连接数")
    heartbeat_interval: int = Field(30, description="心跳间隔(秒)")


# 导出所有模型
__all__ = [
    "MarketData",
    "TickData", 
    "TechnicalIndicator",
    "MarketDataResponse",
    "TechnicalIndicatorResponse",
    "MarketDataBatch",
    "TechnicalIndicatorBatch",
    "DataPipelineStatus",
    "WebSocketMessage",
    "DataCollectorConfig",
    "ProcessorConfig",
    "IndicatorConfig",
    "StorageConfig",
    "DistributorConfig"
]
