"""
海天AI纳斯达克交易系统 - 数据管道API端点
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 数据管道管理和监控API
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, Depends, Request
from fastapi.responses import JSONResponse

from app.data_pipeline import get_global_pipeline
from app.schemas.market import (
    MarketData, TechnicalIndicator, DataPipelineStatus,
    MarketDataBatch, TechnicalIndicatorBatch, WebSocketMessage
)
from app.core.exceptions import BusinessLogicException

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/status")
async def get_pipeline_status():
    """获取数据管道状态"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        stats = pipeline.get_statistics()

        # 构建测试期望的响应格式
        return {
            "is_running": stats["is_running"],
            "start_time": stats["start_time"],
            "runtime_seconds": stats["runtime_seconds"],
            "statistics": {
                "processed_count": stats["processed_count"],
                "error_count": stats["error_count"],
                "error_rate": stats["error_rate"],
                "active_tasks": stats["active_tasks"]
            },
            "components": {
                "data_collector": "operational" if hasattr(pipeline, 'data_collector') and pipeline.data_collector else "inactive",
                "market_data_processor": "operational" if hasattr(pipeline, 'market_data_processor') and pipeline.market_data_processor else "inactive",
                "tick_data_processor": "operational" if hasattr(pipeline, 'tick_data_processor') and pipeline.tick_data_processor else "inactive",
                "technical_indicators": "operational" if hasattr(pipeline, 'technical_indicators') and pipeline.technical_indicators else "inactive",
                "websocket_distributor": "operational" if hasattr(pipeline, 'websocket_distributor') and pipeline.websocket_distributor else "inactive",
                "ai_trader_distributor": "operational" if hasattr(pipeline, 'ai_trader_distributor') and pipeline.ai_trader_distributor else "inactive",
                "cache_storage": "operational" if hasattr(pipeline, 'cache_storage') and pipeline.cache_storage else "inactive",
                "database_storage": "operational" if hasattr(pipeline, 'database_storage') and pipeline.database_storage else "inactive"
            }
        }

    except Exception as e:
        logger.error(f"获取数据管道状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.post("/start")
async def start_pipeline():
    """启动数据管道"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未初始化")

        if pipeline.is_running:
            return {"message": "数据管道已在运行", "status": "already_running"}

        await pipeline.start()
        return {"message": "数据管道启动成功", "status": "started"}

    except Exception as e:
        logger.error(f"启动数据管道失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")


@router.post("/stop")
async def stop_pipeline():
    """停止数据管道"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未初始化")

        if not pipeline.is_running:
            return {"message": "数据管道已停止", "status": "already_stopped"}

        await pipeline.stop()
        return {"message": "数据管道停止成功", "status": "stopped"}

    except Exception as e:
        logger.error(f"停止数据管道失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")


@router.post("/restart")
async def restart_pipeline():
    """重启数据管道"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未初始化")

        # 先停止
        if pipeline.is_running:
            await pipeline.stop()

        # 再启动
        await pipeline.start()
        return {"message": "数据管道重启成功", "status": "restarted"}

    except Exception as e:
        logger.error(f"重启数据管道失败: {e}")
        raise HTTPException(status_code=500, detail=f"重启失败: {str(e)}")


@router.get("/collector/statistics")
async def get_collector_statistics():
    """获取数据采集器统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        if not pipeline.data_collector:
            raise HTTPException(status_code=503, detail="数据采集器未初始化")

        stats = pipeline.data_collector.get_statistics()
        return stats

    except Exception as e:
        logger.error(f"获取采集器统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/processor/statistics")
async def get_processor_statistics():
    """获取数据处理器统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        # 返回嵌套的处理器统计信息
        stats = {}

        if pipeline.market_data_processor:
            stats["market_data_processor"] = pipeline.market_data_processor.get_statistics()

        if pipeline.tick_data_processor:
            stats["tick_data_processor"] = pipeline.tick_data_processor.get_statistics()

        return stats

    except Exception as e:
        logger.error(f"获取处理器统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/indicators/statistics")
async def get_indicators_statistics():
    """获取技术指标统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        # 构建技术指标统计信息
        indicators = {}

        if pipeline.technical_indicators:
            for name, calculator in pipeline.technical_indicators.items():
                # 检查计算器是否有get_statistics方法
                if hasattr(calculator, 'get_statistics'):
                    indicator_stats = calculator.get_statistics()
                    indicators[name] = indicator_stats
                else:
                    # 提供基本的统计信息
                    indicators[name] = {
                        "calculated_count": 0,
                        "error_count": 0,
                        "calculation_rate": 0.0,
                        "name": name,
                        "type": calculator.__class__.__name__,
                        "status": "active"
                    }

        return {
            "total_indicators": len(indicators),
            "indicators": indicators
        }

    except Exception as e:
        logger.error(f"获取技术指标统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/distributors/statistics")
async def get_distributors_statistics():
    """获取数据分发器统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        stats = {}

        if pipeline.websocket_distributor:
            ws_stats = pipeline.websocket_distributor.get_statistics()
            stats["websocket_distributor"] = ws_stats

        if pipeline.ai_trader_distributor:
            ai_stats = pipeline.ai_trader_distributor.get_statistics()
            stats["ai_trader_distributor"] = ai_stats

        return stats

    except Exception as e:
        logger.error(f"获取分发器统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/storage/statistics")
async def get_storage_statistics():
    """获取存储组件统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        stats = {}

        if pipeline.cache_storage:
            cache_stats = pipeline.cache_storage.get_statistics()
            stats["cache_storage"] = cache_stats

        if pipeline.database_storage:
            db_stats = pipeline.database_storage.get_statistics()
            stats["database_storage"] = db_stats

        return stats

    except Exception as e:
        logger.error(f"获取存储统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/market-data/{symbol}", response_model=List[MarketData])
async def get_market_data(
    symbol: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取最新市场数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(hours=1)
        
        # 从数据库存储获取历史数据
        if pipeline.database_storage:
            data_list = await pipeline.database_storage.get_market_data(
                symbol, start_time, end_time
            )
            return data_list[:limit]
        else:
            return []
            
    except Exception as e:
        logger.error(f"获取历史市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")


@router.get("/market-data/{symbol}/history", response_model=List[MarketData])
async def get_market_data_history(
    symbol: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取历史市场数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(days=7)  # 默认7天历史数据

        # 从数据库存储获取历史数据
        if pipeline.database_storage:
            data_list = await pipeline.database_storage.get_market_data(
                symbol, start_time, end_time
            )
            return data_list[:limit]
        else:
            return []

    except Exception as e:
        logger.error(f"获取历史市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")


@router.get("/indicators/{symbol}", response_model=List[TechnicalIndicator])
async def get_technical_indicators(
    symbol: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取技术指标数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(hours=1)

        # 从数据库存储获取所有技术指标数据
        all_indicators = []
        if pipeline.database_storage:
            # 获取所有可用的技术指标
            indicator_names = ["RSI", "MACD", "MA_20", "EMA_12", "BOLLINGER"]
            for indicator_name in indicator_names:
                try:
                    data_list = await pipeline.database_storage.get_technical_indicators(
                        symbol, indicator_name, start_time, end_time
                    )
                    all_indicators.extend(data_list)
                except Exception:
                    continue  # 忽略单个指标的错误

        return all_indicators[:limit]

    except Exception as e:
        logger.error(f"获取技术指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/indicators/{symbol}/{indicator_name}", response_model=List[TechnicalIndicator])
async def get_technical_indicators_history(
    symbol: str,
    indicator_name: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取历史技术指标数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(hours=1)
        
        # 从数据库存储获取历史数据
        if pipeline.database_storage:
            data_list = await pipeline.database_storage.get_technical_indicators(
                symbol, indicator_name, start_time, end_time
            )
            return data_list[:limit]
        else:
            return []
            
    except Exception as e:
        logger.error(f"获取历史技术指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/statistics")
async def get_pipeline_statistics():
    """获取数据管道详细统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        stats = {
            "pipeline": pipeline.get_statistics(),
            "collector": pipeline.data_collector.get_statistics() if pipeline.data_collector else {},
            "processor": {
                "market_data": pipeline.market_data_processor.get_statistics() if pipeline.market_data_processor else {},
                "tick_data": pipeline.tick_data_processor.get_statistics() if pipeline.tick_data_processor else {}
            },
            "indicators": {
                name: calc.calculated_count 
                for name, calc in pipeline.technical_indicators.items()
            },
            "distributors": {
                "websocket": pipeline.websocket_distributor.get_statistics() if pipeline.websocket_distributor else {},
                "ai_trader": pipeline.ai_trader_distributor.get_statistics() if pipeline.ai_trader_distributor else {}
            },
            "storage": {
                "database": pipeline.database_storage.get_statistics() if pipeline.database_storage else {},
                "cache": pipeline.cache_storage.get_statistics() if pipeline.cache_storage else {}
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"获取管道统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/connections")
async def get_websocket_connections():
    """获取WebSocket连接信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline or not pipeline.websocket_distributor:
            raise HTTPException(status_code=503, detail="WebSocket分发器未启动")
        
        return pipeline.websocket_distributor.get_connection_info()
        
    except Exception as e:
        logger.error(f"获取连接信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取连接失败: {str(e)}")


@router.post("/subscribe")
async def subscribe_trader(subscription_data: dict):
    """AI交易员订阅数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        trader_id = subscription_data.get("trader_id")
        symbols = subscription_data.get("symbols", [])
        callback_url = subscription_data.get("callback_url")

        if not trader_id:
            raise HTTPException(status_code=422, detail="trader_id is required")

        # 创建一个简单的回调函数
        async def trader_callback(data):
            logger.info(f"AI交易员 {trader_id} 收到数据: {type(data).__name__}")

        await pipeline.subscribe_ai_trader(trader_id, trader_callback)

        return {
            "message": f"AI交易员 {trader_id} 订阅成功",
            "trader_id": trader_id,
            "symbols": symbols,
            "callback_url": callback_url
        }

    except HTTPException:
        # 重新抛出HTTP异常，保持原始状态码
        raise
    except Exception as e:
        logger.error(f"AI交易员订阅失败: {e}")
        raise HTTPException(status_code=500, detail=f"订阅失败: {str(e)}")


@router.post("/ai-trader/subscribe/{trader_id}")
async def subscribe_ai_trader(trader_id: str, request: Request):
    """AI交易员订阅数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 这里需要实现AI交易员的回调函数
        # 暂时使用一个简单的回调函数作为示例
        async def trader_callback(data):
            logger.info(f"AI交易员 {trader_id} 收到数据: {type(data).__name__}")
        
        await pipeline.subscribe_ai_trader(trader_id, trader_callback)
        
        return {"message": f"AI交易员 {trader_id} 订阅成功"}
        
    except Exception as e:
        logger.error(f"AI交易员订阅失败: {e}")
        raise HTTPException(status_code=500, detail=f"订阅失败: {str(e)}")


@router.delete("/ai-trader/unsubscribe/{trader_id}")
async def unsubscribe_ai_trader(trader_id: str):
    """AI交易员取消订阅"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        await pipeline.unsubscribe_ai_trader(trader_id)
        
        return {"message": f"AI交易员 {trader_id} 取消订阅成功"}
        
    except Exception as e:
        logger.error(f"AI交易员取消订阅失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消订阅失败: {str(e)}")


@router.get("/config")
async def get_pipeline_configuration():
    """获取数据管道配置信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")

        config = {
            "collector": {
                "type": "MockDataCollector",
                "enabled": True,
                "collection_interval": 1.0
            },
            "processors": {
                "market_data_processor": {
                    "enabled": True,
                    "batch_size": 100
                },
                "tick_data_processor": {
                    "enabled": True,
                    "buffer_size": 1000
                }
            },
            "indicators": {
                "RSI": {"period": 14, "enabled": True},
                "MACD": {"fast": 12, "slow": 26, "signal": 9, "enabled": True},
                "MA_20": {"period": 20, "enabled": True},
                "EMA_12": {"period": 12, "enabled": True},
                "BOLLINGER": {"period": 20, "std_dev": 2, "enabled": True}
            },
            "distributors": {
                "websocket": {"enabled": True, "max_connections": 100},
                "ai_trader": {"enabled": True, "max_subscribers": 50}
            },
            "storage": {
                "cache": {"enabled": True, "max_size": 10000},
                "database": {"enabled": True, "batch_size": 100}
            }
        }

        return config

    except Exception as e:
        logger.error(f"获取管道配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.get("/health")
async def get_pipeline_health():
    """获取数据管道健康状态"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            return {
                "status": "unhealthy",
                "message": "数据管道未启动",
                "components": {}
            }

        # 检查各组件健康状态
        components = {}

        current_time = datetime.now().isoformat()

        # 检查数据采集器
        if pipeline.data_collector:
            components["collector"] = {
                "status": "healthy" if pipeline.data_collector.is_running else "stopped",
                "type": pipeline.data_collector.__class__.__name__,
                "last_check": current_time
            }

        # 检查处理器
        if pipeline.market_data_processor:
            components["market_processor"] = {
                "status": "healthy",
                "last_check": current_time
            }
        if pipeline.tick_data_processor:
            components["tick_processor"] = {
                "status": "healthy",
                "last_check": current_time
            }

        # 检查技术指标
        if pipeline.technical_indicators:
            components["indicators"] = {
                "status": "healthy",
                "count": len(pipeline.technical_indicators),
                "last_check": current_time
            }

        # 检查分发器
        if pipeline.websocket_distributor:
            components["websocket_distributor"] = {
                "status": "healthy",
                "last_check": current_time
            }
        if pipeline.ai_trader_distributor:
            components["ai_trader_distributor"] = {
                "status": "healthy",
                "last_check": current_time
            }

        # 检查存储
        if pipeline.cache_storage:
            components["cache_storage"] = {
                "status": "healthy",
                "last_check": current_time
            }
        if pipeline.database_storage:
            components["database_storage"] = {
                "status": "healthy",
                "last_check": current_time
            }

        overall_status = "healthy" if pipeline.is_running else "degraded"

        return {
            "status": overall_status,
            "message": "数据管道运行正常" if overall_status == "healthy" else "数据管道部分功能异常",
            "components": components,
            "uptime_seconds": (datetime.now() - pipeline.start_time).total_seconds() if pipeline.start_time else 0,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取管道健康状态失败: {e}")
        return {
            "status": "unhealthy",
            "message": f"健康检查失败: {str(e)}",
            "components": {}
        }


@router.websocket("/ws/{connection_id}")
async def websocket_endpoint(websocket: WebSocket, connection_id: str):
    """WebSocket实时数据推送端点"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            await websocket.close(code=1011, reason="数据管道未启动")
            return
        
        # 添加WebSocket连接
        await pipeline.add_websocket_connection(
            connection_id, 
            websocket, 
            metadata={
                "connected_at": datetime.now().isoformat(),
                "client_info": websocket.headers.get("user-agent", "unknown")
            }
        )
        
        logger.info(f"WebSocket连接已建立: {connection_id}")
        
        try:
            # 保持连接活跃
            while True:
                # 等待客户端消息（心跳或其他控制消息）
                try:
                    message = await websocket.receive_text()
                    logger.debug(f"收到WebSocket消息: {connection_id} - {message}")
                    
                    # 可以在这里处理客户端发送的控制消息
                    # 比如订阅特定标的、设置过滤条件等
                    
                except Exception as e:
                    logger.debug(f"WebSocket接收消息异常: {e}")
                    break
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接断开: {connection_id}")
        except Exception as e:
            logger.error(f"WebSocket连接异常: {connection_id} - {e}")
        finally:
            # 移除连接
            await pipeline.remove_websocket_connection(connection_id)
            logger.info(f"WebSocket连接已清理: {connection_id}")
            
    except Exception as e:
        logger.error(f"WebSocket端点异常: {e}")
        try:
            await websocket.close(code=1011, reason=f"服务器错误: {str(e)}")
        except:
            pass


@router.post("/test/send-data")
async def send_test_data():
    """发送测试数据（仅用于开发测试）"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 创建测试市场数据
        test_data = MarketData(
            symbol="159509",
            current_price=2.5,
            open_price=2.48,
            high_price=2.52,
            low_price=2.46,
            volume=1000000,
            timestamp=datetime.now()
        )
        
        # 手动触发数据处理
        await pipeline._handle_raw_data(test_data)
        
        return {"message": "测试数据发送成功", "data": test_data}
        
    except Exception as e:
        logger.error(f"发送测试数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"发送失败: {str(e)}")


# 导出路由
__all__ = ["router"]
